import { ConvertContentResponse } from '@/types/api';

export class ContentConverter {
  async convert(
    content: string,
    type: 'html' | 'markdown',
    options?: {
      preserveFormatting?: boolean;
      autoDetectComponents?: boolean;
    }
  ): Promise<ConvertContentResponse> {
    const { autoDetectComponents = true } = options || {};

    let tiptapJson: any;
    const suggestions: string[] = [];
    const componentUsage = {
      imageContainer: 0,
      highlightBox: 0,
      codeBlock: 0,
      section: 0,
      divider: 0,
    };

    // 预处理内容，清理和标准化
    const cleanContent = this.preprocessContent(content, type);

    if (type === 'html') {
      tiptapJson = this.convertHtmlToTipTap(cleanContent, autoDetectComponents, componentUsage, suggestions);
    } else {
      tiptapJson = this.convertMarkdownToTipTap(cleanContent, autoDetectComponents, componentUsage, suggestions);
    }

    // 后处理：优化结构和添加智能建议
    tiptapJson = this.postprocessTipTap(tiptapJson, componentUsage, suggestions);

    return {
      tiptapJson,
      suggestions,
      componentUsage,
    };
  }

  // 预处理内容
  private preprocessContent(content: string, type: 'html' | 'markdown'): string {
    let processed = content.trim();

    if (type === 'html') {
      // 清理HTML：移除不必要的属性和标签
      processed = processed
        .replace(/style="[^"]*"/g, '') // 移除内联样式
        .replace(/class="[^"]*"/g, '') // 移除CSS类
        .replace(/<span[^>]*>/g, '').replace(/<\/span>/g, '') // 移除span标签
        .replace(/<div([^>]*)>/g, '<p$1>').replace(/<\/div>/g, '</p>') // div转p
        .replace(/\s+/g, ' ') // 标准化空白字符
        .replace(/>\s+</g, '><'); // 移除标签间空白
    } else {
      // 清理Markdown：标准化格式
      processed = processed
        .replace(/\r\n/g, '\n') // 统一换行符
        .replace(/\n{3,}/g, '\n\n') // 限制连续空行
        .replace(/[ \t]+$/gm, '') // 移除行尾空白
        .replace(/^[ \t]+/gm, ''); // 移除行首空白（保留列表缩进）
    }

    return processed;
  }

  private convertHtmlToTipTap(
    html: string,
    autoDetectComponents: boolean,
    componentUsage: any,
    suggestions: string[]
  ): any {
    const doc = {
      type: 'doc',
      content: [] as any[],
    };

    // 使用更智能的HTML解析
    let parser: DOMParser;
    let htmlDoc: Document;
    let container: Element | null = null;

    if (typeof window !== 'undefined') {
      // 浏览器环境
      parser = new DOMParser();
      htmlDoc = parser.parseFromString(`<div>${html}</div>`, 'text/html');
      container = htmlDoc.querySelector('div');
    } else {
      // 服务器端环境，使用简单的正则解析
      return this.parseHtmlWithRegex(html, autoDetectComponents, componentUsage, suggestions);
    }

    if (!container) {
      return doc;
    }

    // 递归处理DOM节点
    const processNode = (node: Node): any[] => {
      const result: any[] = [];

      if (node.nodeType === Node.TEXT_NODE) {
        const text = node.textContent?.trim();
        if (text) {
          return [{ type: 'text', text }];
        }
        return [];
      }

      if (node.nodeType === Node.ELEMENT_NODE) {
        const element = node as Element;
        const tagName = element.tagName.toLowerCase();

        switch (tagName) {
          case 'h1':
          case 'h2':
          case 'h3':
          case 'h4':
          case 'h5':
          case 'h6':
            const level = parseInt(tagName.charAt(1));
            const headingContent = this.processChildNodes(element);
            if (headingContent.length > 0) {
              result.push({
                type: 'heading',
                attrs: { level },
                content: headingContent,
              });
            }
            break;

          case 'p':
            const paragraphContent = this.processChildNodes(element);
            if (paragraphContent.length > 0) {
              result.push({
                type: 'paragraph',
                content: paragraphContent,
              });
            }
            break;

          case 'img':
            const src = element.getAttribute('src');
            const alt = element.getAttribute('alt') || '';
            const title = element.getAttribute('title');

            if (src) {
              result.push({
                type: 'image',
                attrs: {
                  src,
                  alt,
                  caption: title,
                },
              });
              componentUsage.imageContainer++;

              if (autoDetectComponents) {
                suggestions.push('检测到图片，建议使用 ImageContainer 组件优化显示效果');
              }
            }
            break;

          case 'pre':
          case 'code':
            const codeContent = element.textContent || '';
            const language = element.getAttribute('class')?.replace('language-', '') || 'text';

            if (codeContent.trim()) {
              result.push({
                type: 'codeBlock',
                attrs: { language },
                content: [{ type: 'text', text: codeContent }],
              });
              componentUsage.codeBlock++;

              if (autoDetectComponents) {
                suggestions.push('检测到代码块，建议使用 CodeBlock 组件提供语法高亮');
              }
            }
            break;

          case 'blockquote':
            const quoteContent = this.processChildNodes(element);
            if (quoteContent.length > 0 && autoDetectComponents) {
              suggestions.push('检测到引用内容，建议使用 HighlightBox 组件突出显示');
              componentUsage.highlightBox++;
            }
            result.push({
              type: 'blockquote',
              content: quoteContent.map(content => ({
                type: 'paragraph',
                content: Array.isArray(content) ? content : [content],
              })),
            });
            break;

          case 'ul':
            const listItems = Array.from(element.children).map(li => ({
              type: 'listItem',
              content: this.processChildNodes(li).map(content => ({
                type: 'paragraph',
                content: Array.isArray(content) ? content : [content],
              })),
            }));
            result.push({
              type: 'bulletList',
              content: listItems,
            });
            break;

          case 'ol':
            const orderedItems = Array.from(element.children).map(li => ({
              type: 'listItem',
              content: this.processChildNodes(li).map(content => ({
                type: 'paragraph',
                content: Array.isArray(content) ? content : [content],
              })),
            }));
            result.push({
              type: 'orderedList',
              content: orderedItems,
            });
            break;

          case 'hr':
            result.push({ type: 'horizontalRule' });
            componentUsage.divider++;

            if (autoDetectComponents) {
              suggestions.push('检测到分割线，建议使用 Divider 组件创建更美观的效果');
            }
            break;

          case 'strong':
          case 'b':
            const strongContent = this.processChildNodes(element);
            return strongContent.map(content => ({
              ...content,
              marks: [...(content.marks || []), { type: 'bold' }],
            }));

          case 'em':
          case 'i':
            const emContent = this.processChildNodes(element);
            return emContent.map(content => ({
              ...content,
              marks: [...(content.marks || []), { type: 'italic' }],
            }));

          case 'a':
            const href = element.getAttribute('href');
            const linkContent = this.processChildNodes(element);
            return linkContent.map(content => ({
              ...content,
              marks: [...(content.marks || []), {
                type: 'link',
                attrs: { href, target: element.getAttribute('target') }
              }],
            }));

          default:
            // 对于其他标签，处理其子节点
            const childContent = this.processChildNodes(element);
            result.push(...childContent.map(content => ({
              type: 'paragraph',
              content: Array.isArray(content) ? content : [content],
            })));
        }
      }

      return result;
    };

    // 处理所有子节点
    for (const child of Array.from(container.childNodes)) {
      const processed = processNode(child);
      doc.content.push(...processed);
    }

    return doc;
  }

  // 辅助方法：处理子节点
  private processChildNodes(element: Element): any[] {
    const result: any[] = [];

    for (const child of Array.from(element.childNodes)) {
      if (child.nodeType === Node.TEXT_NODE) {
        const text = child.textContent?.trim();
        if (text) {
          result.push({ type: 'text', text });
        }
      } else if (child.nodeType === Node.ELEMENT_NODE) {
        const childElement = child as Element;
        const tagName = childElement.tagName.toLowerCase();

        switch (tagName) {
          case 'strong':
          case 'b':
            const strongText = childElement.textContent?.trim();
            if (strongText) {
              result.push({
                type: 'text',
                text: strongText,
                marks: [{ type: 'bold' }]
              });
            }
            break;

          case 'em':
          case 'i':
            const emText = childElement.textContent?.trim();
            if (emText) {
              result.push({
                type: 'text',
                text: emText,
                marks: [{ type: 'italic' }]
              });
            }
            break;

          case 'code':
            const codeText = childElement.textContent?.trim();
            if (codeText) {
              result.push({
                type: 'text',
                text: codeText,
                marks: [{ type: 'code' }]
              });
            }
            break;

          case 'a':
            const linkText = childElement.textContent?.trim();
            const href = childElement.getAttribute('href');
            if (linkText && href) {
              result.push({
                type: 'text',
                text: linkText,
                marks: [{
                  type: 'link',
                  attrs: {
                    href,
                    target: childElement.getAttribute('target')
                  }
                }]
              });
            }
            break;

          default:
            const text = childElement.textContent?.trim();
            if (text) {
              result.push({ type: 'text', text });
            }
        }
      }
    }

    return result;
  }

  private convertMarkdownToTipTap(
    markdown: string,
    autoDetectComponents: boolean,
    componentUsage: any,
    suggestions: string[]
  ): any {
    const doc = {
      type: 'doc',
      content: [] as any[],
    };

    const lines = markdown.split('\n');
    let i = 0;

    while (i < lines.length) {
      const line = lines[i];
      const trimmedLine = line.trim();

      // 跳过空行
      if (!trimmedLine) {
        i++;
        continue;
      }

      // 检测标题
      if (trimmedLine.startsWith('#')) {
        const level = trimmedLine.match(/^#+/)?.[0].length || 1;
        const text = trimmedLine.replace(/^#+\s*/, '');

        doc.content.push({
          type: 'heading',
          attrs: { level: Math.min(level, 6) },
          content: this.parseInlineMarkdown(text),
        });
        i++;
        continue;
      }

      // 检测代码块
      if (trimmedLine.startsWith('```')) {
        const language = trimmedLine.replace('```', '').trim() || 'text';
        const codeLines: string[] = [];
        i++; // 跳过开始标记

        // 收集代码内容
        while (i < lines.length && !lines[i].trim().startsWith('```')) {
          codeLines.push(lines[i]);
          i++;
        }

        if (i < lines.length) {
          i++; // 跳过结束标记
        }

        const codeContent = codeLines.join('\n');
        if (codeContent.trim()) {
          doc.content.push({
            type: 'codeBlock',
            attrs: { language },
            content: [{ type: 'text', text: codeContent }],
          });
          componentUsage.codeBlock++;

          if (autoDetectComponents) {
            suggestions.push(`检测到${language}代码块，建议使用 CodeBlock 组件提供语法高亮`);
          }
        }
        continue;
      }

      // 检测图片
      if (trimmedLine.includes('![')) {
        const match = trimmedLine.match(/!\[([^\]]*)\]\(([^)]+)(?:\s+"([^"]+)")?\)/);
        if (match) {
          doc.content.push({
            type: 'image',
            attrs: {
              src: match[2],
              alt: match[1],
              caption: match[3], // 可选的标题
            },
          });
          componentUsage.imageContainer++;

          if (autoDetectComponents) {
            suggestions.push('检测到图片，建议使用 ImageContainer 组件优化显示效果');
          }
        }
        i++;
        continue;
      }

      // 检测分割线
      if (trimmedLine === '---' || trimmedLine === '***' || trimmedLine === '___') {
        doc.content.push({
          type: 'horizontalRule',
        });
        componentUsage.divider++;

        if (autoDetectComponents) {
          suggestions.push('检测到分割线，建议使用 Divider 组件创建更美观的效果');
        }
        i++;
        continue;
      }

      // 检测引用块
      if (trimmedLine.startsWith('>')) {
        const quoteLines: string[] = [];

        // 收集连续的引用行
        while (i < lines.length && lines[i].trim().startsWith('>')) {
          const quoteLine = lines[i].trim().replace(/^>\s*/, '');
          if (quoteLine) {
            quoteLines.push(quoteLine);
          }
          i++;
        }

        if (quoteLines.length > 0) {
          const quoteContent = quoteLines.join(' ');
          doc.content.push({
            type: 'blockquote',
            content: [{
              type: 'paragraph',
              content: this.parseInlineMarkdown(quoteContent),
            }],
          });

          if (autoDetectComponents) {
            suggestions.push('检测到引用内容，建议使用 HighlightBox 组件突出显示重要信息');
            componentUsage.highlightBox++;
          }
        }
        continue;
      }

      // 检测无序列表
      if (trimmedLine.match(/^[-*+]\s/)) {
        const listItems: any[] = [];

        // 收集连续的列表项
        while (i < lines.length && lines[i].trim().match(/^[-*+]\s/)) {
          const itemText = lines[i].trim().replace(/^[-*+]\s/, '');
          listItems.push({
            type: 'listItem',
            content: [{
              type: 'paragraph',
              content: this.parseInlineMarkdown(itemText),
            }],
          });
          i++;
        }

        if (listItems.length > 0) {
          doc.content.push({
            type: 'bulletList',
            content: listItems,
          });
        }
        continue;
      }

      // 检测有序列表
      if (trimmedLine.match(/^\d+\.\s/)) {
        const listItems: any[] = [];

        // 收集连续的列表项
        while (i < lines.length && lines[i].trim().match(/^\d+\.\s/)) {
          const itemText = lines[i].trim().replace(/^\d+\.\s/, '');
          listItems.push({
            type: 'listItem',
            content: [{
              type: 'paragraph',
              content: this.parseInlineMarkdown(itemText),
            }],
          });
          i++;
        }

        if (listItems.length > 0) {
          doc.content.push({
            type: 'orderedList',
            content: listItems,
          });
        }
        continue;
      }

      // 默认段落处理
      const paragraphLines: string[] = [trimmedLine];
      i++;

      // 收集连续的非空行作为段落
      while (i < lines.length && lines[i].trim() &&
             !lines[i].trim().startsWith('#') &&
             !lines[i].trim().startsWith('```') &&
             !lines[i].trim().startsWith('>') &&
             !lines[i].trim().match(/^[-*+]\s/) &&
             !lines[i].trim().match(/^\d+\.\s/) &&
             !['---', '***', '___'].includes(lines[i].trim())) {
        paragraphLines.push(lines[i].trim());
        i++;
      }

      const paragraphText = paragraphLines.join(' ');
      if (paragraphText) {
        doc.content.push({
          type: 'paragraph',
          content: this.parseInlineMarkdown(paragraphText),
        });
      }
    }

    return doc;
  }

  // 解析内联Markdown格式
  private parseInlineMarkdown(text: string): any[] {
    // 简单的内联格式解析
    const patterns = [
      { regex: /\*\*(.*?)\*\*/g, mark: 'bold' },
      { regex: /\*(.*?)\*/g, mark: 'italic' },
      { regex: /`(.*?)`/g, mark: 'code' },
      { regex: /\[([^\]]+)\]\(([^)]+)\)/g, mark: 'link' },
    ];

    // 如果没有特殊格式，直接返回文本
    if (!patterns.some(p => p.regex.test(text))) {
      return [{ type: 'text', text }];
    }

    // 这里可以实现更复杂的内联解析
    // 为了简化，暂时返回基本文本
    return [{ type: 'text', text }];
  }

  // 后处理：优化TipTap结构和添加智能建议
  private postprocessTipTap(tiptapJson: any, componentUsage: any, suggestions: string[]): any {
    // 清理空内容
    if (tiptapJson.content) {
      tiptapJson.content = tiptapJson.content.filter((node: any) => {
        if (node.type === 'paragraph' && (!node.content || node.content.length === 0)) {
          return false;
        }
        return true;
      });
    }

    // 添加智能建议
    this.addSmartSuggestions(tiptapJson, componentUsage, suggestions);

    // 优化结构
    this.optimizeStructure(tiptapJson);

    return tiptapJson;
  }

  // 添加智能建议
  private addSmartSuggestions(tiptapJson: any, componentUsage: any, suggestions: string[]): void {
    const totalComponents = Object.values(componentUsage).reduce((sum: number, count: any) => sum + (count as number), 0);

    // 基于内容分析添加建议
    if (totalComponents === 0) {
      suggestions.push('💡 建议：您的内容可以通过使用WeChat组件来增强视觉效果');
    }

    if (componentUsage.imageContainer > 0 && componentUsage.highlightBox === 0) {
      suggestions.push('💡 建议：考虑添加 HighlightBox 组件来突出重要信息');
    }

    if (componentUsage.codeBlock > 2) {
      suggestions.push('💡 建议：多个代码块可以考虑使用 Section 组件进行分组');
    }

    if (tiptapJson.content && tiptapJson.content.length > 10 && componentUsage.divider === 0) {
      suggestions.push('💡 建议：长文章可以使用 Divider 组件分割内容，提高可读性');
    }

    // 检查内容结构
    const hasHeadings = tiptapJson.content?.some((node: any) => node.type === 'heading');
    if (!hasHeadings && tiptapJson.content?.length > 5) {
      suggestions.push('💡 建议：添加标题来组织文章结构，提高可读性');
    }
  }

  // 优化文档结构
  private optimizeStructure(tiptapJson: any): void {
    if (!tiptapJson.content) return;

    // 合并连续的段落（如果它们应该是一个段落）
    const optimizedContent: any[] = [];
    let currentParagraph: any = null;

    for (const node of tiptapJson.content) {
      if (node.type === 'paragraph' && currentParagraph) {
        // 检查是否应该合并
        const currentText = this.extractTextFromNode(currentParagraph);
        const nodeText = this.extractTextFromNode(node);

        // 如果当前段落很短且下一个段落不是以大写字母开头，可能需要合并
        if (currentText.length < 50 && nodeText && !nodeText.match(/^[A-Z]/)) {
          // 合并段落
          currentParagraph.content.push({ type: 'text', text: ' ' });
          currentParagraph.content.push(...node.content);
          continue;
        }
      }

      if (currentParagraph) {
        optimizedContent.push(currentParagraph);
      }

      if (node.type === 'paragraph') {
        currentParagraph = { ...node };
      } else {
        currentParagraph = null;
        optimizedContent.push(node);
      }
    }

    if (currentParagraph) {
      optimizedContent.push(currentParagraph);
    }

    tiptapJson.content = optimizedContent;
  }

  // 从节点提取文本
  private extractTextFromNode(node: any): string {
    if (!node.content) return '';

    return node.content
      .filter((item: any) => item.type === 'text')
      .map((item: any) => item.text)
      .join('');
  }

  // 服务器端HTML解析（使用正则表达式）
  private parseHtmlWithRegex(
    html: string,
    autoDetectComponents: boolean,
    componentUsage: any,
    suggestions: string[]
  ): any {
    const doc = {
      type: 'doc',
      content: [] as any[],
    };

    // 简单的HTML标签匹配
    const lines = html.split('\n').filter(line => line.trim());

    for (const line of lines) {
      const trimmedLine = line.trim();

      if (!trimmedLine) continue;

      // 检测图片
      if (trimmedLine.includes('<img')) {
        const srcMatch = trimmedLine.match(/src="([^"]+)"/);
        const altMatch = trimmedLine.match(/alt="([^"]+)"/);

        if (srcMatch) {
          doc.content.push({
            type: 'image',
            attrs: {
              src: srcMatch[1],
              alt: altMatch ? altMatch[1] : '',
            },
          });
          componentUsage.imageContainer++;

          if (autoDetectComponents) {
            suggestions.push('建议使用 ImageContainer 组件优化图片显示');
          }
        }
        continue;
      }

      // 检测标题
      if (trimmedLine.startsWith('<h')) {
        const levelMatch = trimmedLine.match(/<h([1-6])/);
        const textMatch = trimmedLine.match(/>([^<]+)</);

        if (levelMatch && textMatch) {
          doc.content.push({
            type: 'heading',
            attrs: { level: parseInt(levelMatch[1]) },
            content: [{ type: 'text', text: textMatch[1] }],
          });
        }
        continue;
      }

      // 检测代码块
      if (trimmedLine.includes('<pre') || trimmedLine.includes('<code')) {
        const codeMatch = trimmedLine.match(/>([^<]+)</);
        if (codeMatch) {
          doc.content.push({
            type: 'codeBlock',
            content: [{ type: 'text', text: codeMatch[1] }],
          });
          componentUsage.codeBlock++;

          if (autoDetectComponents) {
            suggestions.push('建议使用 CodeBlock 组件提供语法高亮');
          }
        }
        continue;
      }

      // 检测分割线
      if (trimmedLine.includes('<hr') || trimmedLine === '---') {
        doc.content.push({
          type: 'horizontalRule',
        });
        componentUsage.divider++;

        if (autoDetectComponents) {
          suggestions.push('建议使用 Divider 组件创建更美观的分割线');
        }
        continue;
      }

      // 默认段落处理
      const cleanText = trimmedLine.replace(/<[^>]+>/g, '').trim();
      if (cleanText) {
        doc.content.push({
          type: 'paragraph',
          content: [{ type: 'text', text: cleanText }],
        });
      }
    }

    return doc;
  }
}
