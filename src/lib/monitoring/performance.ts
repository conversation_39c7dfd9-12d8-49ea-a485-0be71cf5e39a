interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: Date;
  tags?: Record<string, string>;
}

interface MemoryMetrics {
  heapUsed: number;
  heapTotal: number;
  external: number;
  rss: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private readonly maxMetrics = 1000; // 保留最近1000个指标

  // 记录性能指标
  recordMetric(name: string, value: number, unit: string, tags?: Record<string, string>) {
    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: new Date(),
      tags,
    };

    this.metrics.push(metric);

    // 保持指标数量在限制内
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // 在开发环境中输出到控制台
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Performance] ${name}: ${value}${unit}`, tags || '');
    }
  }

  // 测量函数执行时间
  async measureAsync<T>(name: string, fn: () => Promise<T>, tags?: Record<string, string>): Promise<T> {
    const startTime = Date.now();
    try {
      const result = await fn();
      const duration = Date.now() - startTime;
      this.recordMetric(name, duration, 'ms', { ...tags, status: 'success' });
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordMetric(name, duration, 'ms', { ...tags, status: 'error' });
      throw error;
    }
  }

  // 测量同步函数执行时间
  measure<T>(name: string, fn: () => T, tags?: Record<string, string>): T {
    const startTime = Date.now();
    try {
      const result = fn();
      const duration = Date.now() - startTime;
      this.recordMetric(name, duration, 'ms', { ...tags, status: 'success' });
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordMetric(name, duration, 'ms', { ...tags, status: 'error' });
      throw error;
    }
  }

  // 获取内存使用情况
  getMemoryMetrics(): MemoryMetrics {
    const memoryUsage = process.memoryUsage();
    return {
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
      external: Math.round(memoryUsage.external / 1024 / 1024), // MB
      rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
    };
  }

  // 记录内存使用情况
  recordMemoryMetrics() {
    const memory = this.getMemoryMetrics();
    this.recordMetric('memory.heap.used', memory.heapUsed, 'MB');
    this.recordMetric('memory.heap.total', memory.heapTotal, 'MB');
    this.recordMetric('memory.external', memory.external, 'MB');
    this.recordMetric('memory.rss', memory.rss, 'MB');
  }

  // 获取指标统计
  getMetricStats(name: string, timeWindow?: number): {
    count: number;
    avg: number;
    min: number;
    max: number;
    latest: number;
  } {
    const now = Date.now();
    const windowMs = timeWindow ? timeWindow * 1000 : 300000; // 默认5分钟
    
    const relevantMetrics = this.metrics.filter(
      metric => 
        metric.name === name && 
        (now - metric.timestamp.getTime()) <= windowMs
    );

    if (relevantMetrics.length === 0) {
      return { count: 0, avg: 0, min: 0, max: 0, latest: 0 };
    }

    const values = relevantMetrics.map(m => m.value);
    const sum = values.reduce((a, b) => a + b, 0);

    return {
      count: relevantMetrics.length,
      avg: sum / relevantMetrics.length,
      min: Math.min(...values),
      max: Math.max(...values),
      latest: relevantMetrics[relevantMetrics.length - 1].value,
    };
  }

  // 获取所有指标
  getAllMetrics(timeWindow?: number): PerformanceMetric[] {
    if (!timeWindow) return [...this.metrics];

    const now = Date.now();
    const windowMs = timeWindow * 1000;
    
    return this.metrics.filter(
      metric => (now - metric.timestamp.getTime()) <= windowMs
    );
  }

  // 清理旧指标
  cleanup(maxAge: number = 3600) { // 默认1小时
    const now = Date.now();
    const maxAgeMs = maxAge * 1000;
    
    this.metrics = this.metrics.filter(
      metric => (now - metric.timestamp.getTime()) <= maxAgeMs
    );
  }

  // 获取系统健康状态
  getHealthStatus(): {
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
    metrics: Record<string, any>;
  } {
    const memory = this.getMemoryMetrics();
    const issues: string[] = [];
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';

    // 检查内存使用
    if (memory.heapUsed > 512) { // 512MB
      issues.push(`高内存使用: ${memory.heapUsed}MB`);
      status = 'warning';
    }

    if (memory.heapUsed > 1024) { // 1GB
      issues.push(`严重内存使用: ${memory.heapUsed}MB`);
      status = 'critical';
    }

    // 检查API响应时间
    const apiStats = this.getMetricStats('api.response.time', 300); // 5分钟窗口
    if (apiStats.avg > 1000) { // 1秒
      issues.push(`API响应时间过慢: ${Math.round(apiStats.avg)}ms`);
      status = status === 'critical' ? 'critical' : 'warning';
    }

    return {
      status,
      issues,
      metrics: {
        memory,
        apiResponseTime: apiStats,
        uptime: process.uptime(),
      },
    };
  }
}

// 单例实例
export const performanceMonitor = new PerformanceMonitor();

// 定期记录内存指标
if (typeof window === 'undefined') { // 只在服务器端运行
  setInterval(() => {
    performanceMonitor.recordMemoryMetrics();
    performanceMonitor.cleanup(); // 清理旧指标
  }, 60000); // 每分钟
}
