import { prisma } from '@/lib/prisma/client';
import {
  Article,
  ArticleSummary,
  CreateArticleRequest,
  UpdateArticleRequest,
  PaginationParams,
  PaginatedResponse
} from '@/types/api';

export class ArticleService {
  async getArticles(params: PaginationParams & {
    status?: string;
    search?: string;
  }): Promise<PaginatedResponse<ArticleSummary>> {
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      status = 'ALL',
      search = '',
    } = params;

    const skip = (page - 1) * limit;
    
    // Build where clause
    const where: any = {};
    
    if (status !== 'ALL') {
      where.status = status;
    }
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { excerpt: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Build orderBy clause
    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    const [articles, total] = await Promise.all([
      prisma.article.findMany({
        where,
        skip,
        take: limit,
        orderBy,
        select: {
          id: true,
          slug: true,
          title: true,
          excerpt: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          // 不选择 contentJson 字段以减少数据传输
        },
      }),
      prisma.article.count({ where }),
    ]);

    return {
      items: articles,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getArticleById(id: string): Promise<Article | null> {
    return await prisma.article.findUnique({
      where: { id },
    });
  }

  async getArticleBySlug(slug: string): Promise<Article | null> {
    return await prisma.article.findUnique({
      where: { slug },
    });
  }

  async createArticle(data: CreateArticleRequest): Promise<Article> {
    return await prisma.article.create({
      data: {
        slug: data.slug,
        title: data.title,
        excerpt: data.excerpt,
        contentJson: JSON.stringify(data.content),
        status: data.status || 'DRAFT',
      },
    });
  }

  async updateArticle(id: string, data: UpdateArticleRequest): Promise<Article> {
    const updateData: any = {};
    
    if (data.slug !== undefined) updateData.slug = data.slug;
    if (data.title !== undefined) updateData.title = data.title;
    if (data.excerpt !== undefined) updateData.excerpt = data.excerpt;
    if (data.content !== undefined) updateData.contentJson = JSON.stringify(data.content);
    if (data.status !== undefined) updateData.status = data.status;

    return await prisma.article.update({
      where: { id },
      data: updateData,
    });
  }

  async deleteArticle(id: string): Promise<{ deleted: boolean }> {
    await prisma.article.delete({
      where: { id },
    });
    
    return { deleted: true };
  }

  async duplicateArticle(id: string): Promise<Article> {
    const original = await this.getArticleById(id);
    if (!original) {
      throw new Error('Article not found');
    }

    const duplicatedData: CreateArticleRequest = {
      slug: `${original.slug}-copy-${Date.now()}`,
      title: `${original.title} (Copy)`,
      excerpt: original.excerpt || undefined,
      content: JSON.parse(original.contentJson),
      status: 'DRAFT',
    };

    return await this.createArticle(duplicatedData);
  }
}

export const articleService = new ArticleService();
