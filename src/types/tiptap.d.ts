import '@tiptap/core';

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    imageContainer: {
      /**
       * 设置图片容器
       */
      setImageContainer: (attributes?: {
        src?: string;
        alt?: string;
        caption?: string;
        width?: string;
        height?: string;
      }) => ReturnType;
      /**
       * 更新图片容器属性
       */
      updateImageContainer: (attributes?: {
        src?: string;
        alt?: string;
        caption?: string;
        width?: string;
        height?: string;
      }) => ReturnType;
    };
    highlightBox: {
      /**
       * 设置高亮框
       */
      setHighlightBox: (attributes?: {
        type?: 'info' | 'warning' | 'success' | 'error';
        title?: string;
      }) => ReturnType;
      /**
       * 切换高亮框
       */
      toggleHighlightBox: (attributes?: {
        type?: 'info' | 'warning' | 'success' | 'error';
        title?: string;
      }) => ReturnType;
      /**
       * 更新高亮框属性
       */
      updateHighlightBox: (attributes?: {
        type?: 'info' | 'warning' | 'success' | 'error';
        title?: string;
      }) => ReturnType;
    };
    codeBlock: {
      /**
       * 设置代码块
       */
      setCodeBlock: (attributes?: {
        language?: string;
        title?: string;
        showLineNumbers?: boolean;
      }) => ReturnType;
      /**
       * 切换代码块
       */
      toggleCodeBlock: (attributes?: {
        language?: string;
        title?: string;
        showLineNumbers?: boolean;
      }) => ReturnType;
      /**
       * 更新代码块属性
       */
      updateCodeBlock: (attributes?: {
        language?: string;
        title?: string;
        showLineNumbers?: boolean;
      }) => ReturnType;
    };
    section: {
      /**
       * 设置章节
       */
      setSection: (attributes?: {
        title?: string;
      }) => ReturnType;
      /**
       * 切换章节
       */
      toggleSection: (attributes?: {
        title?: string;
      }) => ReturnType;
      /**
       * 更新章节属性
       */
      updateSection: (attributes?: {
        title?: string;
      }) => ReturnType;
    };
    divider: {
      /**
       * 设置分割线
       */
      setDivider: (attributes?: {
        style?: any;
      }) => ReturnType;
    };
  }
}
