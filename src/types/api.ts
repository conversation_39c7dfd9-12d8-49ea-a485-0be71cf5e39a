// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Article Types
export interface Article {
  id: string;
  slug: string;
  title: string;
  excerpt?: string | null;
  contentJson: string;
  status: 'DRAFT' | 'PUBLISHED';
  createdAt: Date;
  updatedAt: Date;
}

// Article summary type (without contentJson for list views)
export interface ArticleSummary {
  id: string;
  slug: string;
  title: string;
  excerpt?: string | null;
  status: 'DRAFT' | 'PUBLISHED';
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateArticleRequest {
  slug: string;
  title: string;
  excerpt?: string;
  content: any; // TipTap JSON
  status?: 'DRAFT' | 'PUBLISHED';
}

export interface UpdateArticleRequest {
  slug?: string;
  title?: string;
  excerpt?: string;
  content?: any;
  status?: 'DRAFT' | 'PUBLISHED';
}

// Content Conversion Types
export interface ConvertContentRequest {
  content: string;
  type: 'html' | 'markdown';
  options?: {
    preserveFormatting?: boolean;
    autoDetectComponents?: boolean;
  };
}

export interface ConvertContentResponse {
  tiptapJson: any;
  suggestions: string[];
  componentUsage: {
    imageContainer: number;
    highlightBox: number;
    codeBlock: number;
    section: number;
    divider: number;
  };
}

// File Upload Types
export interface UploadImageResponse {
  url: string;
  filename: string;
  size: number;
  dimensions: {
    width: number;
    height: number;
  };
}

// Export Types
export interface ExportWeChatRequest {
  articleId: string;
  options?: {
    inlineStyles?: boolean;
    optimizeImages?: boolean;
  };
}

export interface ExportWeChatResponse {
  html: string;
  css: string;
  images: string[];
}
