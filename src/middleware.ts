import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  const startTime = Date.now();
  
  // 创建响应
  const response = NextResponse.next();
  
  // 添加性能监控头
  response.headers.set('X-Request-Start', startTime.toString());
  response.headers.set('X-Request-ID', crypto.randomUUID());
  
  // 添加安全头
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // 在开发环境中添加调试信息
  if (process.env.NODE_ENV === 'development') {
    response.headers.set('X-Debug-Path', request.nextUrl.pathname);
    response.headers.set('X-Debug-Method', request.method);
  }
  
  return response;
}

export const config = {
  matcher: [
    '/',
    '/admin/:path*',
    '/posts/:path*',
  ],
};
