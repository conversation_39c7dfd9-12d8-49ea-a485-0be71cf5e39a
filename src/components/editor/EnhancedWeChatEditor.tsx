'use client';

import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import '@/styles/enhanced-editor.css';
import {
  Bold,
  Italic,
  Heading2,
  Image as ImageIcon,
  Code,
  Minus,
  AlertCircle,
  Layout
} from 'lucide-react';

// 导入自定义扩展
import { ImageContainerExtension } from './extensions/ImageContainerExtension';
import { HighlightBoxExtension } from './extensions/HighlightBoxExtension';
import { CodeBlockExtension } from './extensions/CodeBlockExtension';
import { SectionExtension, DividerExtension } from './extensions/SectionExtension';

// 导入内容转换器
import { ContentConverter } from '@/lib/converters/content-converter';

interface EnhancedWeChatEditorProps {
  content: any;
  onChange: (content: any) => void;
  className?: string;
}

export function EnhancedWeChatEditor({ content, onChange, className }: EnhancedWeChatEditorProps) {
  const [isLoading, setIsLoading] = useState(false);
  const contentConverter = new ContentConverter();

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        codeBlock: false, // 禁用默认代码块，使用自定义的
      }),
      ImageContainerExtension,
      HighlightBoxExtension,
      CodeBlockExtension,
      SectionExtension,
      DividerExtension,
    ],
    content,
    immediatelyRender: false, // 修复SSR问题
    onUpdate: ({ editor }) => {
      onChange(editor.getJSON());
    },
    editorProps: {
      attributes: {
        class: 'prose prose-invert max-w-none focus:outline-none min-h-[400px] p-4',
      },
      // 处理粘贴事件
      handlePaste: (view, event) => {
        const clipboardData = event.clipboardData;
        if (!clipboardData) return false;

        // 检查是否有HTML内容
        const htmlContent = clipboardData.getData('text/html');
        const textContent = clipboardData.getData('text/plain');

        if (htmlContent && htmlContent.trim()) {
          // 阻止默认粘贴行为
          event.preventDefault();
          
          // 异步处理HTML转换
          handleHtmlPaste(htmlContent);
          return true;
        }

        // 如果没有HTML内容，允许默认行为
        return false;
      },
    },
  });

  // 处理HTML粘贴转换
  const handleHtmlPaste = async (htmlContent: string) => {
    if (!editor) return;

    setIsLoading(true);
    try {
      // 使用内容转换器将HTML转换为TipTap JSON
      const result = await contentConverter.convert(htmlContent, 'html', {
        autoDetectComponents: true,
      });

      if (result.tiptapJson && result.tiptapJson.content) {
        // 在当前光标位置插入转换后的内容
        const { content: convertedContent } = result.tiptapJson;

        if (convertedContent && Array.isArray(convertedContent)) {
          convertedContent.forEach((node: any) => {
            editor.commands.insertContent(node);
          });
        }

        // 显示转换建议（如果有）
        if (result.suggestions && result.suggestions.length > 0) {
          console.log('转换建议:', result.suggestions);
          // 这里可以添加UI提示用户转换建议
        }
      } else {
        // 如果转换失败，插入纯文本
        const textContent = htmlContent.replace(/<[^>]*>/g, '');
        editor.commands.insertContent(textContent);
      }
    } catch (error) {
      console.error('HTML转换失败:', error);
      // 插入纯文本作为备选
      const textContent = htmlContent.replace(/<[^>]*>/g, '');
      editor.commands.insertContent(textContent);
    } finally {
      setIsLoading(false);
    }
  };

  // 工具栏按钮功能
  const addImageContainer = () => {
    const url = window.prompt('请输入图片URL:');
    if (url && editor) {
      const alt = window.prompt('请输入图片描述:') || '';
      const caption = window.prompt('请输入图片说明（可选）:') || '';
      (editor.chain().focus() as any).setImageContainer({
        src: url,
        alt,
        caption: caption || undefined
      }).run();
    }
  };

  const addHighlightBox = () => {
    if (editor) {
      const title = window.prompt('请输入高亮框标题（可选）:') || '';
      (editor.chain().focus() as any).setHighlightBox({
        type: 'info',
        title: title || undefined
      }).run();
    }
  };

  const addCodeBlock = () => {
    if (editor) {
      const language = window.prompt('请输入编程语言（如：javascript, python）:') || 'text';
      const title = window.prompt('请输入代码块标题（可选）:') || '';
      (editor.chain().focus() as any).setCodeBlock({
        language,
        title: title || undefined
      }).run();
    }
  };

  const addSection = () => {
    if (editor) {
      const title = window.prompt('请输入章节标题（可选）:') || '';
      (editor.chain().focus() as any).setSection({
        title: title || undefined
      }).run();
    }
  };

  const addDivider = () => {
    if (editor) {
      (editor.chain().focus() as any).setDivider().run();
    }
  };

  if (!editor) {
    return <div className="p-4 text-slate-400">编辑器加载中...</div>;
  }

  return (
    <div className={`border border-slate-600 rounded-lg bg-slate-800 ${className}`}>
      {/* 工具栏 */}
      <div className="border-b border-slate-600 p-3 flex flex-wrap gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={`border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer ${
            editor.isActive('bold') ? 'bg-slate-700' : ''
          }`}
        >
          <Bold className="w-4 h-4" />
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={`border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer ${
            editor.isActive('italic') ? 'bg-slate-700' : ''
          }`}
        >
          <Italic className="w-4 h-4" />
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
          className={`border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer ${
            editor.isActive('heading', { level: 2 }) ? 'bg-slate-700' : ''
          }`}
        >
          <Heading2 className="w-4 h-4" />
        </Button>

        <div className="w-px h-6 bg-slate-600 mx-1" />

        <Button
          variant="outline"
          size="sm"
          onClick={addImageContainer}
          className="border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer"
        >
          <ImageIcon className="w-4 h-4" />
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={addHighlightBox}
          className="border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer"
        >
          <AlertCircle className="w-4 h-4" />
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={addCodeBlock}
          className="border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer"
        >
          <Code className="w-4 h-4" />
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={addSection}
          className="border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer"
        >
          <Layout className="w-4 h-4" />
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={addDivider}
          className="border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer"
        >
          <Minus className="w-4 h-4" />
        </Button>

        {isLoading && (
          <div className="ml-auto flex items-center text-cyan-400 text-sm">
            <div className="animate-spin w-4 h-4 border-2 border-cyan-400 border-t-transparent rounded-full mr-2"></div>
            转换中...
          </div>
        )}
      </div>

      {/* 编辑器内容区域 */}
      <div className="relative">
        <EditorContent 
          editor={editor} 
          className="prose-editor text-white"
        />
        
        {/* 粘贴提示 */}
        <div className="absolute bottom-2 right-2 text-xs text-slate-500">
          💡 支持粘贴HTML内容自动转换为WeChat组件
        </div>
      </div>
    </div>
  );
}
