import { Node, mergeAttributes } from '@tiptap/core';
import { ReactNodeViewRenderer } from '@tiptap/react';
// 导入React节点视图组件
import { ImageContainerNodeView } from './ImageContainerNodeView';

// 定义ImageContainer节点的属性接口
export interface ImageContainerAttributes {
  src: string;
  alt: string;
  width?: string | number;
  height?: string | number;
  caption?: string;
}

// ImageContainer扩展
export const ImageContainerExtension = Node.create({
  name: 'imageContainer',
  
  group: 'block',
  
  atom: true,
  
  draggable: true,
  
  addAttributes() {
    return {
      src: {
        default: null,
        parseHTML: element => element.getAttribute('src'),
        renderHTML: attributes => {
          if (!attributes.src) {
            return {};
          }
          return { src: attributes.src };
        },
      },
      alt: {
        default: '',
        parseHTML: element => element.getAttribute('alt'),
        renderHTML: attributes => {
          if (!attributes.alt) {
            return {};
          }
          return { alt: attributes.alt };
        },
      },
      width: {
        default: '100%',
        parseHTML: element => element.getAttribute('data-width'),
        renderHTML: attributes => {
          if (!attributes.width) {
            return {};
          }
          return { 'data-width': attributes.width };
        },
      },
      height: {
        default: 'auto',
        parseHTML: element => element.getAttribute('data-height'),
        renderHTML: attributes => {
          if (!attributes.height) {
            return {};
          }
          return { 'data-height': attributes.height };
        },
      },
      caption: {
        default: null,
        parseHTML: element => element.getAttribute('data-caption'),
        renderHTML: attributes => {
          if (!attributes.caption) {
            return {};
          }
          return { 'data-caption': attributes.caption };
        },
      },
    };
  },
  
  parseHTML() {
    return [
      {
        tag: 'div[data-type="image-container"]',
      },
      {
        tag: 'img',
        getAttrs: element => {
          const img = element as HTMLImageElement;
          return {
            src: img.src,
            alt: img.alt,
            width: img.getAttribute('data-width') || '100%',
            height: img.getAttribute('data-height') || 'auto',
            caption: img.getAttribute('data-caption'),
          };
        },
      },
    ];
  },
  
  renderHTML({ HTMLAttributes }) {
    return [
      'div',
      mergeAttributes(HTMLAttributes, { 'data-type': 'image-container' }),
      [
        'img',
        {
          src: HTMLAttributes.src,
          alt: HTMLAttributes.alt,
          'data-width': HTMLAttributes.width,
          'data-height': HTMLAttributes.height,
          'data-caption': HTMLAttributes.caption,
        },
      ],
    ];
  },
  
  addCommands() {
    return {
      setImageContainer: (attributes?: ImageContainerAttributes) => ({ commands }: any) => {
        return commands.insertContent({
          type: this.name,
          attrs: attributes || { src: '', alt: '' },
        });
      },
      updateImageContainer: (attributes?: Partial<ImageContainerAttributes>) => ({ commands }: any) => {
        return commands.updateAttributes(this.name, attributes || {});
      },
    } as any;
  },
  
  addNodeView() {
    return ReactNodeViewRenderer(ImageContainerNodeView);
  },
});
