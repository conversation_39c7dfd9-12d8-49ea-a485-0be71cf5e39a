'use client';

import React, { useState } from 'react';
import { NodeViewWrapper, NodeViewProps } from '@tiptap/react';
import { ImageContainer } from '@/components/posts/ImageContainer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Edit, Check, X, Upload } from 'lucide-react';

export function ImageContainerNodeView({ node, updateAttributes, selected }: NodeViewProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    src: node.attrs.src || '',
    alt: node.attrs.alt || '',
    width: node.attrs.width || '100%',
    height: node.attrs.height || 'auto',
    caption: node.attrs.caption || '',
  });

  const handleSave = () => {
    updateAttributes(editData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditData({
      src: node.attrs.src || '',
      alt: node.attrs.alt || '',
      width: node.attrs.width || '100%',
      height: node.attrs.height || 'auto',
      caption: node.attrs.caption || '',
    });
    setIsEditing(false);
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // 这里可以实现图片上传逻辑
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setEditData(prev => ({ ...prev, src: result }));
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <NodeViewWrapper className={`image-container-node ${selected ? 'ProseMirror-selectednode' : ''}`}>
      {isEditing ? (
        <Card className="bg-slate-800 border-slate-600">
          <CardHeader className="pb-3">
            <CardTitle className="text-white text-sm flex items-center gap-2">
              <Edit className="w-4 h-4" />
              编辑图片
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="image-src" className="text-slate-300">图片URL</Label>
              <div className="flex gap-2">
                <Input
                  id="image-src"
                  value={editData.src}
                  onChange={(e) => setEditData(prev => ({ ...prev, src: e.target.value }))}
                  placeholder="输入图片URL或上传图片"
                  className="bg-slate-700 border-slate-600 text-white"
                />
                <Button
                  variant="outline"
                  size="sm"
                  className="border-slate-600 text-slate-300 cursor-pointer hover:bg-slate-700"
                  onClick={() => document.getElementById('image-upload')?.click()}
                >
                  <Upload className="w-4 h-4" />
                </Button>
                <input
                  id="image-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  className="hidden"
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="image-alt" className="text-slate-300">图片描述</Label>
              <Input
                id="image-alt"
                value={editData.alt}
                onChange={(e) => setEditData(prev => ({ ...prev, alt: e.target.value }))}
                placeholder="图片描述文字"
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            
            <div>
              <Label htmlFor="image-caption" className="text-slate-300">图片说明</Label>
              <Input
                id="image-caption"
                value={editData.caption}
                onChange={(e) => setEditData(prev => ({ ...prev, caption: e.target.value }))}
                placeholder="图片说明文字（可选）"
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            
            <div className="flex gap-2">
              <div className="flex-1">
                <Label htmlFor="image-width" className="text-slate-300">宽度</Label>
                <Input
                  id="image-width"
                  value={editData.width}
                  onChange={(e) => setEditData(prev => ({ ...prev, width: e.target.value }))}
                  placeholder="100%"
                  className="bg-slate-700 border-slate-600 text-white"
                />
              </div>
              <div className="flex-1">
                <Label htmlFor="image-height" className="text-slate-300">高度</Label>
                <Input
                  id="image-height"
                  value={editData.height}
                  onChange={(e) => setEditData(prev => ({ ...prev, height: e.target.value }))}
                  placeholder="auto"
                  className="bg-slate-700 border-slate-600 text-white"
                />
              </div>
            </div>
            
            <div className="flex gap-2 pt-2">
              <Button
                onClick={handleSave}
                size="sm"
                className="bg-green-600 hover:bg-green-700 cursor-pointer"
              >
                <Check className="w-4 h-4 mr-1" />
                保存
              </Button>
              <Button
                onClick={handleCancel}
                variant="outline"
                size="sm"
                className="border-slate-600 text-slate-300 cursor-pointer hover:bg-slate-700"
              >
                <X className="w-4 h-4 mr-1" />
                取消
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="relative group">
          <ImageContainer
            src={node.attrs.src}
            alt={node.attrs.alt}
            width={node.attrs.width}
            height={node.attrs.height}
            caption={node.attrs.caption}
          />
          {selected && (
            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                onClick={() => setIsEditing(true)}
                size="sm"
                variant="outline"
                className="bg-slate-800/80 border-slate-600 text-white hover:bg-slate-700 cursor-pointer"
              >
                <Edit className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>
      )}
    </NodeViewWrapper>
  );
}
