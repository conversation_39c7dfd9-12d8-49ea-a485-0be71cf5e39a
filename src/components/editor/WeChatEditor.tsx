'use client';

import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  Bold,
  Italic,
  Heading2,
  Image as ImageIcon,
  Code,
  Minus,
  AlertCircle,
  Layout
} from 'lucide-react';

// 导入自定义扩展
import {
  ImageContainerExtension,
  HighlightBoxExtension,
  CodeBlockExtension,
  SectionExtension,
  DividerExtension
} from './extensions';

interface WeChatEditorProps {
  content?: any;
  onChange?: (content: any) => void;
  editable?: boolean;
}

export function WeChatEditor({ content, onChange, editable = true }: WeChatEditorProps) {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        heading: {
          levels: [1, 2, 3],
        },
        codeBlock: false, // 禁用默认代码块，使用自定义的
      }),
      // WeChat自定义组件扩展
      ImageContainerExtension,
      HighlightBoxExtension,
      CodeBlockExtension,
      SectionExtension,
      DividerExtension,
      // 保留基础图片扩展作为备用
      Image.configure({
        HTMLAttributes: {
          class: 'max-w-full h-auto rounded-lg',
        },
      }),
    ],
    content: content || {
      type: 'doc',
      content: [
        {
          type: 'paragraph',
          content: [{ type: 'text', text: '开始编写您的文章...' }],
        },
      ],
    },
    editable,
    immediatelyRender: false, // 修复SSR问题
    onUpdate: ({ editor }) => {
      onChange?.(editor.getJSON());
    },
    editorProps: {
      attributes: {
        class: 'prose prose-lg max-w-none focus:outline-none min-h-[400px] p-4 bg-slate-800 text-white rounded-lg border border-slate-600',
      },
    },
  });

  // 插入图片容器
  const addImageContainer = () => {
    const url = window.prompt('请输入图片URL:');
    if (url && editor) {
      const alt = window.prompt('请输入图片描述:') || '';
      const caption = window.prompt('请输入图片说明（可选）:') || '';
      (editor.chain().focus() as any).setImageContainer({
        src: url,
        alt,
        caption: caption || undefined
      }).run();
    }
  };

  // 插入高亮框
  const addHighlightBox = () => {
    if (editor) {
      const title = window.prompt('请输入高亮框标题（可选）:') || '';
      (editor.chain().focus() as any).setHighlightBox({
        type: 'info',
        title: title || undefined
      }).run();
    }
  };

  // 插入代码块
  const addCodeBlock = () => {
    if (editor) {
      const language = window.prompt('请输入编程语言（如：javascript, python）:') || 'text';
      const title = window.prompt('请输入代码块标题（可选）:') || '';
      (editor.chain().focus() as any).setCodeBlock({
        language,
        title: title || undefined
      }).run();
    }
  };

  // 插入章节
  const addSection = () => {
    if (editor) {
      const title = window.prompt('请输入章节标题（可选）:') || '';
      (editor.chain().focus() as any).setSection({
        title: title || undefined
      }).run();
    }
  };

  // 插入分割线
  const addDivider = () => {
    if (editor) {
      (editor.chain().focus() as any).setDivider().run();
    }
  };

  if (!editor) {
    return <div className="p-4 text-center text-slate-400">加载编辑器中...</div>;
  }

  return (
    <div className="wechat-editor bg-slate-900 rounded-lg border border-slate-700">
      {/* 工具栏 */}
      <div className="border-b border-slate-700 p-3 flex gap-2 flex-wrap bg-slate-800 rounded-t-lg">
        {/* 基础格式 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleBold().run()}
          className={`${
            editor.isActive('bold') ? 'bg-slate-600 text-cyan-400' : 'text-slate-300 hover:text-white'
          }`}
        >
          <Bold className="w-4 h-4" />
        </Button>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleItalic().run()}
          className={`${
            editor.isActive('italic') ? 'bg-slate-600 text-cyan-400' : 'text-slate-300 hover:text-white'
          }`}
        >
          <Italic className="w-4 h-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 bg-slate-600" />

        {/* 标题 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
          className={`${
            editor.isActive('heading', { level: 2 }) ? 'bg-slate-600 text-cyan-400' : 'text-slate-300 hover:text-white'
          }`}
        >
          <Heading2 className="w-4 h-4" />
        </Button>

        <Separator orientation="vertical" className="h-6 bg-slate-600" />

        {/* WeChat组件 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={addImageContainer}
          className="text-slate-300 hover:text-white"
          title="插入图片容器"
        >
          <ImageIcon className="w-4 h-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={addHighlightBox}
          className="text-slate-300 hover:text-white"
          title="插入高亮框"
        >
          <AlertCircle className="w-4 h-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={addCodeBlock}
          className={`${
            editor.isActive('codeBlock') ? 'bg-slate-600 text-cyan-400' : 'text-slate-300 hover:text-white'
          }`}
          title="插入代码块"
        >
          <Code className="w-4 h-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={addSection}
          className="text-slate-300 hover:text-white"
          title="插入章节"
        >
          <Layout className="w-4 h-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={addDivider}
          className="text-slate-300 hover:text-white"
          title="插入分割线"
        >
          <Minus className="w-4 h-4" />
        </Button>
      </div>

      {/* 编辑器内容 */}
      <EditorContent editor={editor} />
    </div>
  );
}
