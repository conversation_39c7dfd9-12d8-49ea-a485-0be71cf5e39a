'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { 
  Home, 
  FileText, 
  Plus, 
  Settings, 
  Eye,
  ChevronRight
} from 'lucide-react';

export function AdminNav() {
  const pathname = usePathname();

  const navItems = [
    {
      href: '/admin',
      label: '控制台',
      icon: Home,
      exact: true
    },
    {
      href: '/admin/articles',
      label: '文章管理',
      icon: FileText,
      exact: false
    },
    {
      href: '/admin/articles/new',
      label: '新建文章',
      icon: Plus,
      exact: false
    },
    {
      href: '/tools',
      label: '工具箱',
      icon: Settings,
      exact: false
    }
  ];

  const isActive = (href: string, exact: boolean) => {
    if (exact) {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  // 生成面包屑导航
  const generateBreadcrumbs = () => {
    const segments = pathname.split('/').filter(Boolean);
    const breadcrumbs = [];
    
    // 添加首页
    breadcrumbs.push({ label: '管理后台', href: '/admin' });
    
    // 根据路径生成面包屑
    if (segments.includes('articles')) {
      breadcrumbs.push({ label: '文章管理', href: '/admin/articles' });
      
      if (segments.includes('new')) {
        breadcrumbs.push({ label: '新建文章', href: '/admin/articles/new' });
      } else if (segments.includes('edit')) {
        breadcrumbs.push({ label: '编辑文章', href: pathname });
      }
    } else if (segments.includes('tools')) {
      breadcrumbs.push({ label: '工具箱', href: '/tools' });
    }
    
    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  return (
    <nav className="bg-slate-800/50 backdrop-blur-md border-b border-slate-700/50 sticky top-0 z-50">
      <div className="container mx-auto px-6 max-w-7xl">
        {/* 主导航栏 */}
        <div className="flex items-center justify-between h-16">
          {/* Logo和品牌 */}
          <Link 
            href="/admin" 
            className="flex items-center space-x-3 group"
          >
            <div className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400 filter drop-shadow-[0_0_15px_rgba(0,212,255,0.3)] group-hover:drop-shadow-[0_0_20px_rgba(0,212,255,0.5)] transition-all duration-300">
              硅基茶馆2077
            </div>
            <div className="text-sm text-slate-400 hidden md:block">
              管理后台
            </div>
          </Link>

          {/* 导航菜单 */}
          <div className="flex items-center space-x-2">
            {navItems.map((item) => {
              const Icon = item.icon;
              const active = isActive(item.href, item.exact);
              
              return (
                <Link key={item.href} href={item.href}>
                  <Button
                    variant={active ? "default" : "ghost"}
                    size="sm"
                    className={`
                      flex items-center space-x-2 transition-all duration-300 cursor-pointer
                      ${active 
                        ? 'bg-gradient-to-r from-cyan-500 to-blue-500 text-white shadow-lg shadow-cyan-500/25 filter drop-shadow-[0_0_10px_rgba(0,212,255,0.3)]' 
                        : 'text-slate-300 hover:text-white hover:bg-slate-700/50'
                      }
                    `}
                  >
                    <Icon className="h-4 w-4" />
                    <span className="hidden md:inline">{item.label}</span>
                  </Button>
                </Link>
              );
            })}
            
            {/* 查看网站按钮 */}
            <Link href="/">
              <Button
                variant="outline"
                size="sm"
                className="border-slate-600 text-slate-300 hover:bg-slate-700/50 hover:text-white transition-all duration-300 cursor-pointer"
              >
                <Eye className="h-4 w-4 mr-2" />
                <span className="hidden md:inline">查看网站</span>
              </Button>
            </Link>
          </div>
        </div>

        {/* 面包屑导航 */}
        {breadcrumbs.length > 1 && (
          <div className="flex items-center space-x-2 py-3 border-t border-slate-700/30">
            {breadcrumbs.map((crumb, index) => (
              <React.Fragment key={crumb.href}>
                {index > 0 && (
                  <ChevronRight className="h-4 w-4 text-slate-500" />
                )}
                <Link
                  href={crumb.href}
                  className={`
                    text-sm transition-colors duration-200
                    ${index === breadcrumbs.length - 1
                      ? 'text-cyan-400 font-medium'
                      : 'text-slate-400 hover:text-slate-300'
                    }
                  `}
                >
                  {crumb.label}
                </Link>
              </React.Fragment>
            ))}
          </div>
        )}
      </div>
    </nav>
  );
}
