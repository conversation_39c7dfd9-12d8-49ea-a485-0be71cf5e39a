'use client';

import React from 'react';
import { AdminNav } from './AdminNav';

interface AdminLayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
}

export function AdminLayout({ children, title, description }: AdminLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* 导航栏 */}
      <AdminNav />
      
      {/* 主要内容区域 */}
      <main className="container mx-auto p-6 max-w-7xl">
        {/* 页面标题区域 */}
        {(title || description) && (
          <div className="mb-8">
            {title && (
              <h1 className="text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400 mb-2 filter drop-shadow-[0_0_20px_rgba(0,212,255,0.3)]">
                {title}
              </h1>
            )}
            {description && (
              <p className="text-slate-300 text-lg filter drop-shadow-[0_0_10px_rgba(255,107,179,0.2)]">
                {description}
              </p>
            )}
          </div>
        )}
        
        {/* 页面内容 */}
        <div className="relative z-10">
          {children}
        </div>
      </main>
      
      {/* 背景效果 */}
      <div className="fixed inset-0 pointer-events-none">
        {/* 赛博朋克背景效果 */}
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-cyan-900/10 via-transparent to-pink-900/10"></div>
        <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(0,212,255,0.01)_50%,transparent_75%)] bg-[length:40px_40px] animate-pulse"></div>
        
        {/* 动态网格背景 */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(0,212,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(0,212,255,0.02)_1px,transparent_1px)] bg-[size:30px_30px]"></div>
      </div>
    </div>
  );
}
