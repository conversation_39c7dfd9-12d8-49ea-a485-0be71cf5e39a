import React from 'react';

interface SectionProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

export function Section({ children, className = '', style = {} }: SectionProps) {
  const defaultStyle = {
    margin: '2rem 0',
    padding: '1rem 0',
    ...style
  };

  return (
    <div className={`section ${className}`} style={defaultStyle}>
      {children}
    </div>
  );
}

interface DividerProps {
  style?: React.CSSProperties;
}

export function Divider({ style = {} }: DividerProps) {
  const defaultStyle = {
    border: 'none',
    borderTop: '2px solid #e5e7eb',
    margin: '2rem 0',
    ...style
  };

  return <hr className="divider" style={defaultStyle} />;
}
