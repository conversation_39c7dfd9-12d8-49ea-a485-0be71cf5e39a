import React from 'react';

interface ImageContainerProps {
  src: string;
  alt: string;
  width?: string | number;
  height?: string | number;
  caption?: string;
}

export function ImageContainer({ 
  src, 
  alt, 
  width = "100%", 
  height = "auto",
  caption 
}: ImageContainerProps) {
  return (
    <div className="image-container" style={{ margin: '1.5rem 0' }}>
      <img 
        src={src} 
        alt={alt} 
        style={{ 
          width, 
          height, 
          borderRadius: '8px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
          display: 'block',
          margin: '0 auto'
        }} 
      />
      {caption && (
        <p style={{
          textAlign: 'center',
          fontSize: '0.875rem',
          color: '#6b7280',
          marginTop: '0.5rem',
          fontStyle: 'italic'
        }}>
          {caption}
        </p>
      )}
    </div>
  );
}
