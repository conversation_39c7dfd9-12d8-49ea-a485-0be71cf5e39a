'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface Article {
  id: string;
  slug: string;
  title: string;
  excerpt?: string;
  status: 'DRAFT' | 'PUBLISHED';
  createdAt: string;
  updatedAt: string;
}

export default function SimpleArticlesPage() {
  const router = useRouter();
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchArticles();
  }, []);

  const fetchArticles = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/articles');
      const result = await response.json();
      
      if (result.success) {
        setArticles(result.data.items);
      } else {
        setError(result.error || '获取文章失败');
      }
    } catch {
      setError('网络错误');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string, title: string) => {
    if (window.confirm(`确定要删除文章 "${title}" 吗？`)) {
      try {
        const response = await fetch(`/api/articles/${id}`, {
          method: 'DELETE',
        });
        
        const result = await response.json();
        if (result.success) {
          alert('删除成功');
          fetchArticles(); // 刷新列表
        } else {
          alert('删除失败：' + result.error);
        }
      } catch {
        alert('删除失败：网络错误');
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto p-6">
        {/* 页面标题 */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-cyan-400 to-pink-400 bg-clip-text text-transparent">
              文章管理 (简化版)
            </h1>
            <p className="text-slate-400 mt-2">管理您的微信公众号文章</p>
          </div>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={fetchArticles}
              disabled={loading}
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              {loading ? '刷新中...' : '刷新'}
            </Button>
            
            <Button 
              className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600"
              onClick={() => router.push('/admin/articles/new')}
            >
              新建文章
            </Button>
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <Card className="mb-6 bg-red-900/20 border-red-700">
            <CardContent className="p-4">
              <p className="text-red-400">{error}</p>
            </CardContent>
          </Card>
        )}

        {/* 加载状态 */}
        {loading && (
          <div className="text-center py-8">
            <p className="text-slate-400">加载中...</p>
          </div>
        )}

        {/* 文章列表 */}
        {!loading && (
          <div className="grid gap-4">
            {articles.length === 0 ? (
              <Card className="bg-slate-800/50 border-slate-700">
                <CardContent className="p-8 text-center">
                  <p className="text-slate-400 text-lg">暂无文章</p>
                  <p className="text-slate-500 mt-2">点击"新建文章"开始创建您的第一篇文章</p>
                </CardContent>
              </Card>
            ) : (
              articles.map((article) => (
                <Card key={article.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-800/70 transition-colors">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-xl font-semibold text-white">{article.title}</h3>
                          <span 
                            className={`px-2 py-1 rounded text-xs font-medium ${
                              article.status === 'PUBLISHED' 
                                ? 'bg-green-600 text-white' 
                                : 'bg-yellow-600 text-white'
                            }`}
                          >
                            {article.status === 'PUBLISHED' ? '已发布' : '草稿'}
                          </span>
                        </div>
                        
                        {article.excerpt && (
                          <p className="text-slate-300 mb-3">{article.excerpt}</p>
                        )}
                        
                        <div className="flex items-center gap-4 text-sm text-slate-400">
                          <span>路径: /{article.slug}</span>
                          <span>更新: {new Date(article.updatedAt).toLocaleDateString('zh-CN')}</span>
                        </div>
                      </div>
                      
                      <div className="flex gap-2 ml-4">
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="border-slate-600 text-slate-300 hover:bg-slate-700"
                          onClick={() => window.open(`/posts/${article.slug}`, '_blank')}
                        >
                          查看
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="border-slate-600 text-slate-300 hover:bg-slate-700"
                          onClick={() => router.push(`/admin/articles/${article.id}/edit`)}
                        >
                          编辑
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="border-red-600 text-red-400 hover:bg-red-600 hover:text-white"
                          onClick={() => handleDelete(article.id, article.title)}
                        >
                          删除
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        )}

        {/* 统计信息 */}
        {!loading && articles.length > 0 && (
          <div className="mt-8 text-center text-slate-500">
            <p>共 {articles.length} 篇文章</p>
          </div>
        )}
      </div>
    </div>
  );
}
