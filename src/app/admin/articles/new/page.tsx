'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { AdminLayout } from '@/components/admin';
import { EnhancedWeChatEditor } from '@/components/editor/EnhancedWeChatEditor';
import { ArticlePreview } from '@/components/editor/ArticlePreview';
import { useArticleStore } from '@/lib/stores/article-store';
import { Save, ArrowLeft, Eye, EyeOff, Settings } from 'lucide-react';

export default function NewArticlePage() {
  const router = useRouter();
  const { createArticle, loading } = useArticleStore();
  
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    status: 'DRAFT' as 'DRAFT' | 'PUBLISHED',
  });
  
  const [content, setContent] = useState({
    type: 'doc',
    content: [
      {
        type: 'paragraph',
        content: [{ type: 'text', text: '开始编写您的文章...' }],
      },
    ],
  });

  const [showPreview, setShowPreview] = useState(true); // 默认显示预览
  const [showSettings, setShowSettings] = useState(false);
  const [saving, setSaving] = useState(false);

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 自动生成slug
    if (field === 'title' && value && !formData.slug) {
      const slug = value
        .toLowerCase()
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      setFormData(prev => ({ ...prev, slug }));
    }
  };

  const handleSave = async () => {
    if (!formData.title.trim()) {
      alert('请输入文章标题');
      return;
    }
    
    if (!formData.slug.trim()) {
      alert('请输入文章路径');
      return;
    }

    setSaving(true);
    try {
      await createArticle({
        title: formData.title,
        slug: formData.slug,
        excerpt: formData.excerpt,
        content,
        status: formData.status,
      });

      alert('文章创建成功！');

      // 根据文章状态决定跳转目标
      if (formData.status === 'PUBLISHED') {
        // 如果是发布状态，跳转到文章详情页面
        router.push(`/posts/${formData.slug}`);
      } else {
        // 如果是草稿状态，跳转到文章列表
        router.push('/admin/articles');
      }
    } catch {
      alert('创建失败，请重试');
    } finally {
      setSaving(false);
    }
  };

  return (
    <AdminLayout title="新建文章" description="创建新的微信公众号文章">
      {/* 顶部操作栏 - 优化布局 */}
      <div className="mb-6">
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 mb-4">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              className="border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer"
              onClick={() => router.push('/admin/articles')}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回列表
            </Button>

            {/* 文章状态指示器 */}
            <div className="flex items-center gap-2 text-sm text-slate-400">
              <span className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></span>
              草稿状态
            </div>
          </div>

          {/* 右侧操作按钮组 */}
          <div className="flex flex-wrap gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
              className="border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer transition-all duration-200"
            >
              <Settings className="w-4 h-4 mr-2" />
              设置
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowPreview(!showPreview)}
              className={`border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer transition-all duration-200 ${
                showPreview ? 'bg-slate-700 border-cyan-500 text-cyan-400' : ''
              }`}
            >
              {showPreview ? <EyeOff className="w-4 h-4 mr-2" /> : <Eye className="w-4 h-4 mr-2" />}
              {showPreview ? '隐藏预览' : '显示预览'}
            </Button>

            <Button
              onClick={handleSave}
              disabled={saving || loading || !formData.title.trim() || !formData.slug.trim()}
              className={`
                bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600
                text-white cursor-pointer transition-all duration-300 shadow-lg
                ${(saving || loading) ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-cyan-500/25 hover:scale-105'}
                disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100
              `}
            >
              <Save className="w-4 h-4 mr-2" />
              {saving ? '保存中...' : '保存文章'}
            </Button>
          </div>
          </div>

          {/* 标题输入框 - 置于顶部 */}
          <div className="mb-4">
            <Input
              value={formData.title}
              onChange={(e) => updateFormData('title', e.target.value)}
              className="bg-slate-800/50 border-slate-600 text-white placeholder:text-slate-400 text-xl font-semibold h-12"
              placeholder="输入文章标题..."
            />
          </div>
        </div>

        {/* 主要内容区域 - 优化的分栏布局 */}
        <div className="flex gap-4 h-[calc(100vh-180px)] min-h-[600px]">
          {/* 左侧：编辑器区域 */}
          <div className={`transition-all duration-300 ${showPreview ? 'w-[55%]' : 'w-full'}`}>
            <Card className="bg-slate-800/50 border-slate-700 h-full flex flex-col shadow-lg">
              <div className="px-4 py-3 border-b border-slate-700/50 bg-slate-800/30">
                <h3 className="text-sm font-medium text-slate-300 flex items-center">
                  <span className="w-2 h-2 bg-cyan-400 rounded-full mr-2 animate-pulse"></span>
                  编辑器
                </h3>
              </div>
              <CardContent className="p-0 flex-1 flex flex-col">
                <EnhancedWeChatEditor
                  content={content}
                  onChange={setContent}
                  className="flex-1 border-0 rounded-none"
                />
              </CardContent>
            </Card>
          </div>

          {/* 右侧：实时预览区域 */}
          {showPreview && (
            <div className="w-[45%] transition-all duration-300">
              <Card className="bg-slate-800/50 border-slate-700 h-full flex flex-col shadow-lg">
                <div className="px-4 py-3 border-b border-slate-700/50 bg-slate-800/30 flex items-center justify-between">
                  <h3 className="text-sm font-medium text-slate-300 flex items-center">
                    <span className="w-2 h-2 bg-pink-400 rounded-full mr-2 animate-pulse"></span>
                    实时预览
                  </h3>
                  <div className="text-xs text-slate-500">
                    {formData.title ? `预览: ${formData.title}` : '未设置标题'}
                  </div>
                </div>
                <CardContent className="p-0 flex-1 overflow-auto">
                  {/* 手机框架模拟 */}
                  <div className="h-full bg-slate-900/50 p-4">
                    <div className="max-w-sm mx-auto bg-white rounded-lg shadow-xl h-full overflow-auto">
                      {/* 手机状态栏模拟 */}
                      <div className="bg-gray-100 px-4 py-2 text-xs text-gray-600 border-b">
                        <div className="flex justify-between items-center">
                          <span>微信公众号</span>
                          <span>预览模式</span>
                        </div>
                      </div>

                      {/* 文章内容预览 */}
                      <div className="p-4">
                        <ArticlePreview
                          content={content}
                          title={formData.title || ''}
                          className="preview-mode text-gray-900"
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        {/* 设置面板 - 可折叠 */}
        {showSettings && (
          <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center">
            <Card className="bg-slate-800 border-slate-700 w-full max-w-md mx-4">
              <CardContent className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-semibold text-white">文章设置</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowSettings(false)}
                    className="border-slate-600 text-slate-300 hover:bg-slate-700 cursor-pointer"
                  >
                    关闭
                  </Button>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="slug" className="text-slate-300">文章路径 *</Label>
                    <Input
                      id="slug"
                      value={formData.slug}
                      onChange={(e) => updateFormData('slug', e.target.value)}
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                      placeholder="article-url-path"
                    />
                    <p className="text-xs text-slate-500 mt-1">
                      访问地址: /posts/{formData.slug || 'article-url-path'}
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="excerpt" className="text-slate-300">文章摘要</Label>
                    <Textarea
                      id="excerpt"
                      value={formData.excerpt}
                      onChange={(e) => updateFormData('excerpt', e.target.value)}
                      className="bg-slate-700 border-slate-600 text-white placeholder:text-slate-400"
                      rows={4}
                      placeholder="简要描述文章内容（可选）"
                    />
                  </div>

                  <div>
                    <Label htmlFor="status" className="text-slate-300">发布状态</Label>
                    <select
                      id="status"
                      value={formData.status}
                      onChange={(e) => updateFormData('status', e.target.value as 'DRAFT' | 'PUBLISHED')}
                      className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white cursor-pointer"
                    >
                      <option value="DRAFT">草稿</option>
                      <option value="PUBLISHED">已发布</option>
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
    </AdminLayout>
  );
}
