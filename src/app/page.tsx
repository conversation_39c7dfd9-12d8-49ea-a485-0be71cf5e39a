import Link from 'next/link';
import { getAllPosts } from '@/lib/posts';
import ShadcnHeader from '@/components/layout/ShadcnHeader';
import { PostCard } from '@/components/post';
import { Button } from '@/components/ui/button';
import { Plus, Palette, FileText, Settings } from 'lucide-react';

export default function Home() {
  const posts = getAllPosts();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-slate-800 relative overflow-hidden">
      {/* 赛博朋克背景效果 */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-cyan-900/20 via-transparent to-pink-900/20"></div>
      <div className="absolute inset-0 bg-[linear-gradient(45deg,transparent_25%,rgba(0,212,255,0.02)_50%,transparent_75%)] bg-[length:60px_60px] animate-pulse"></div>

      {/* 动态网格背景 */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(0,212,255,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(0,212,255,0.03)_1px,transparent_1px)] bg-[size:50px_50px]"></div>

      {/* 浮动粒子效果 */}
      <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-cyan-400 rounded-full animate-ping opacity-20"></div>
      <div className="absolute top-3/4 right-1/4 w-1 h-1 bg-pink-400 rounded-full animate-pulse opacity-30"></div>
      <div className="absolute top-1/2 left-3/4 w-1.5 h-1.5 bg-purple-400 rounded-full animate-bounce opacity-25"></div>

      <ShadcnHeader />
      
      <main className="container mx-auto px-4 py-8 relative z-10">
        <div className="text-center mb-12">
          {/* 主标题增强 */}
          <div className="relative mb-6">
            <h1 className="text-4xl md:text-6xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 mb-4 relative z-10 filter drop-shadow-[0_0_30px_rgba(0,212,255,0.3)]">
              硅基茶馆2077
            </h1>
            {/* 发光效果 */}
            <div className="absolute inset-0 text-4xl md:text-6xl font-bold text-cyan-400/20 blur-xl animate-pulse">
              硅基茶馆2077
            </div>
          </div>

          {/* 副标题增强 */}
          <div className="relative mb-8">
            <p className="text-xl text-slate-300 mb-2 filter drop-shadow-[0_0_10px_rgba(255,107,179,0.2)]">
              探索未来科技与思考的交汇点
            </p>
            <p className="text-sm text-cyan-400/70 font-mono tracking-wider">
              Silicon Based Teahouse • Est. 2077
            </p>
          </div>

          {/* 按钮组增强 */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white border-0 shadow-lg shadow-cyan-500/25 hover:shadow-cyan-500/40 transition-all duration-300 filter drop-shadow-[0_0_15px_rgba(0,212,255,0.3)]">
              <Link href="/admin/articles/new" className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                新建文章
              </Link>
            </Button>
            <Button asChild size="lg" variant="outline" className="bg-gradient-to-r from-purple-500/10 to-indigo-500/10 border-purple-500/30 hover:bg-gradient-to-r hover:from-purple-500/20 hover:to-indigo-500/20 text-purple-400 hover:text-purple-300 shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40 transition-all duration-300 filter drop-shadow-[0_0_15px_rgba(147,51,234,0.2)]">
              <Link href="/admin" className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                管理后台
              </Link>
            </Button>
            <Button asChild size="lg" variant="outline" className="bg-gradient-to-r from-pink-500/10 to-purple-500/10 border-pink-500/30 hover:bg-gradient-to-r hover:from-pink-500/20 hover:to-purple-500/20 text-pink-400 hover:text-pink-300 shadow-lg shadow-pink-500/25 hover:shadow-pink-500/40 transition-all duration-300 filter drop-shadow-[0_0_15px_rgba(255,107,179,0.2)]">
              <Link href="/cover" className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                制作封面
              </Link>
            </Button>
          </div>
        </div>

        {/* 功能展示区域 */}
        <div className="my-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-pink-400 mb-4 filter drop-shadow-[0_0_20px_rgba(0,212,255,0.3)]">
              强大的功能特性
            </h2>
            <p className="text-slate-400 text-lg">
              为现代内容创作者打造的完整解决方案
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            {/* WeChat组件系统 */}
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/10 to-blue-500/10 rounded-xl border border-cyan-500/20 group-hover:border-cyan-500/40 transition-all duration-300"></div>
              <div className="relative p-6 text-center">
                <div className="text-4xl mb-4 filter drop-shadow-[0_0_15px_rgba(0,212,255,0.3)]">📱</div>
                <h3 className="text-lg font-semibold text-cyan-400 mb-2">WeChat组件</h3>
                <p className="text-slate-400 text-sm">
                  专为微信公众号优化的组件系统，包含图片容器、高亮框、代码块等
                </p>
              </div>
            </div>

            {/* 双模式编辑器 */}
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-xl border border-purple-500/20 group-hover:border-purple-500/40 transition-all duration-300"></div>
              <div className="relative p-6 text-center">
                <div className="text-4xl mb-4 filter drop-shadow-[0_0_15px_rgba(147,51,234,0.3)]">✏️</div>
                <h3 className="text-lg font-semibold text-purple-400 mb-2">双模式编辑</h3>
                <p className="text-slate-400 text-sm">
                  富文本与Markdown编辑模式自由切换，满足不同创作习惯
                </p>
              </div>
            </div>

            {/* 实时预览 */}
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-br from-pink-500/10 to-rose-500/10 rounded-xl border border-pink-500/20 group-hover:border-pink-500/40 transition-all duration-300"></div>
              <div className="relative p-6 text-center">
                <div className="text-4xl mb-4 filter drop-shadow-[0_0_15px_rgba(255,107,179,0.3)]">👁️</div>
                <h3 className="text-lg font-semibold text-pink-400 mb-2">实时预览</h3>
                <p className="text-slate-400 text-sm">
                  手机框架模拟，实时预览文章在微信中的显示效果
                </p>
              </div>
            </div>

            {/* 赛博朋克主题 */}
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-cyan-500/10 rounded-xl border border-indigo-500/20 group-hover:border-indigo-500/40 transition-all duration-300"></div>
              <div className="relative p-6 text-center">
                <div className="text-4xl mb-4 filter drop-shadow-[0_0_15px_rgba(99,102,241,0.3)]">🌟</div>
                <h3 className="text-lg font-semibold text-indigo-400 mb-2">赛博朋克</h3>
                <p className="text-slate-400 text-sm">
                  未来感十足的界面设计，霓虹色彩与动态效果的完美结合
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {posts.length > 0 ? (
            posts.map((post) => (
              <PostCard key={post.slug} post={post} />
            ))
          ) : (
            <div className="col-span-full text-center py-12 relative">
              {/* 空状态背景效果 */}
              <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/5 to-pink-500/5 rounded-xl border border-cyan-500/10"></div>

              <div className="relative z-10">
                <div className="text-6xl mb-4 filter drop-shadow-[0_0_20px_rgba(0,212,255,0.3)]">📝</div>
                <h3 className="text-xl text-slate-300 mb-2 filter drop-shadow-[0_0_10px_rgba(255,107,179,0.2)]">还没有文章</h3>
                <p className="text-slate-400 mb-6">开始您的创作之旅吧！</p>
                <Button asChild size="lg" className="bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-600 hover:to-blue-600 text-white border-0 shadow-lg shadow-cyan-500/25 hover:shadow-cyan-500/40 transition-all duration-300 filter drop-shadow-[0_0_15px_rgba(0,212,255,0.3)]">
                  <Link href="/admin/articles/new" className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    创建第一篇文章
                  </Link>
                </Button>
              </div>
            </div>
          )}
        </div>

        <div className="mt-12 text-center">
          <div className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-slate-800/50 to-slate-700/50 backdrop-blur-sm rounded-full text-slate-300 text-sm border border-cyan-500/20 shadow-lg shadow-cyan-500/10">
            <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse filter drop-shadow-[0_0_5px_rgba(34,197,94,0.5)]"></span>
            <span className="filter drop-shadow-[0_0_5px_rgba(0,212,255,0.2)]">
              共 {posts.length} 篇文章
            </span>
          </div>
        </div>
      </main>
    </div>
  );
}
