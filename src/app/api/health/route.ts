import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma/client';

export async function GET() {
  const startTime = Date.now();

  try {
    // 检查数据库连接和性能
    const dbStart = Date.now();
    await prisma.$queryRaw`SELECT 1`;
    const dbResponseTime = Date.now() - dbStart;

    // 获取文章统计信息
    const [totalArticles, publishedArticles, draftArticles] = await Promise.all([
      prisma.article.count(),
      prisma.article.count({ where: { status: 'PUBLISHED' } }),
      prisma.article.count({ where: { status: 'DRAFT' } })
    ]);

    const responseTime = Date.now() - startTime;
    const memoryUsage = process.memoryUsage();

    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',

      // 性能指标
      performance: {
        responseTime: `${responseTime}ms`,
        dbResponseTime: `${dbResponseTime}ms`,
        uptime: `${Math.floor(process.uptime())}s`,
      },

      // 内存使用情况
      memory: {
        used: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
        total: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
        external: `${Math.round(memoryUsage.external / 1024 / 1024)}MB`,
        rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
      },

      // 数据库状态
      database: {
        status: 'connected',
        responseTime: `${dbResponseTime}ms`,
      },

      // 业务数据统计
      statistics: {
        totalArticles,
        publishedArticles,
        draftArticles,
      },

      // 系统信息
      system: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
      }
    });
  } catch (error) {
    const responseTime = Date.now() - startTime;

    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : '未知错误',
        responseTime: `${responseTime}ms`,
        database: {
          status: 'disconnected',
          error: error instanceof Error ? error.message : '数据库连接失败'
        }
      },
      { status: 500 }
    );
  }
}
