import { NextRequest, NextResponse } from 'next/server';
import { performanceMonitor } from '@/lib/monitoring/performance';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const timeWindow = parseInt(searchParams.get('timeWindow') || '300'); // 默认5分钟
    const metricName = searchParams.get('metric');

    if (metricName) {
      // 返回特定指标的统计信息
      const stats = performanceMonitor.getMetricStats(metricName, timeWindow);
      return NextResponse.json({
        success: true,
        data: {
          metric: metricName,
          timeWindow: `${timeWindow}s`,
          stats,
        },
      });
    }

    // 返回系统健康状态和所有指标
    const healthStatus = performanceMonitor.getHealthStatus();
    const allMetrics = performanceMonitor.getAllMetrics(timeWindow);
    
    // 按指标名称分组
    const groupedMetrics: Record<string, any[]> = {};
    allMetrics.forEach(metric => {
      if (!groupedMetrics[metric.name]) {
        groupedMetrics[metric.name] = [];
      }
      groupedMetrics[metric.name].push({
        value: metric.value,
        unit: metric.unit,
        timestamp: metric.timestamp,
        tags: metric.tags,
      });
    });

    // 计算每个指标的统计信息
    const metricStats: Record<string, any> = {};
    Object.keys(groupedMetrics).forEach(name => {
      metricStats[name] = performanceMonitor.getMetricStats(name, timeWindow);
    });

    return NextResponse.json({
      success: true,
      data: {
        health: healthStatus,
        timeWindow: `${timeWindow}s`,
        metrics: {
          stats: metricStats,
          raw: groupedMetrics,
        },
        summary: {
          totalMetrics: allMetrics.length,
          uniqueMetrics: Object.keys(groupedMetrics).length,
          timestamp: new Date().toISOString(),
        },
      },
    });
  } catch (error) {
    console.error('Error fetching monitoring data:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: '获取监控数据失败',
        message: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 }
    );
  }
}

// 清理旧指标的端点
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const maxAge = parseInt(searchParams.get('maxAge') || '3600'); // 默认1小时

    performanceMonitor.cleanup(maxAge);

    return NextResponse.json({
      success: true,
      message: `已清理超过 ${maxAge} 秒的旧指标`,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error cleaning up metrics:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: '清理指标失败',
        message: error instanceof Error ? error.message : '未知错误',
      },
      { status: 500 }
    );
  }
}
