/* Enhanced WeChat Editor Styles */

.prose-editor {
  color: white;
}

.prose-editor .ProseMirror {
  outline: none;
  padding: 1rem;
  min-height: 400px;
  color: white;
  line-height: 1.6;
}

.prose-editor .ProseMirror p {
  margin: 0.75rem 0;
  color: white;
}

.prose-editor .ProseMirror h1,
.prose-editor .ProseMirror h2,
.prose-editor .ProseMirror h3 {
  color: #06b6d4; /* cyan-500 */
  font-weight: 600;
  margin: 1.5rem 0 1rem 0;
}

.prose-editor .ProseMirror h2 {
  font-size: 1.5rem;
  border-bottom: 2px solid #334155; /* slate-700 */
  padding-bottom: 0.5rem;
}

.prose-editor .ProseMirror strong {
  color: #f1c40f; /* 金色强调 */
  font-weight: 600;
}

.prose-editor .ProseMirror em {
  color: #e879f9; /* fuchsia-400 */
  font-style: italic;
}

.prose-editor .ProseMirror code {
  background: #1e293b; /* slate-800 */
  color: #06b6d4; /* cyan-500 */
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.875rem;
}

.prose-editor .ProseMirror blockquote {
  border-left: 4px solid #06b6d4; /* cyan-500 */
  padding-left: 1rem;
  margin: 1rem 0;
  color: #cbd5e1; /* slate-300 */
  font-style: italic;
  background: rgba(6, 182, 212, 0.1);
  border-radius: 0 0.5rem 0.5rem 0;
}

.prose-editor .ProseMirror ul,
.prose-editor .ProseMirror ol {
  padding-left: 1.5rem;
  margin: 1rem 0;
}

.prose-editor .ProseMirror li {
  margin: 0.5rem 0;
  color: white;
}

.prose-editor .ProseMirror ul li::marker {
  color: #06b6d4; /* cyan-500 */
}

.prose-editor .ProseMirror ol li::marker {
  color: #06b6d4; /* cyan-500 */
  font-weight: 600;
}

/* WeChat 组件样式 */
.prose-editor .wechat-image-container {
  margin: 1.5rem 0;
  border: 2px solid #334155; /* slate-700 */
  border-radius: 0.5rem;
  overflow: hidden;
  background: #1e293b; /* slate-800 */
}

.prose-editor .wechat-highlight-box {
  margin: 1.5rem 0;
  border-radius: 0.5rem;
  padding: 1rem;
  border-left: 4px solid;
}

.prose-editor .wechat-highlight-box.info {
  border-left-color: #06b6d4; /* cyan-500 */
  background: rgba(6, 182, 212, 0.1);
}

.prose-editor .wechat-highlight-box.warning {
  border-left-color: #f59e0b; /* amber-500 */
  background: rgba(245, 158, 11, 0.1);
}

.prose-editor .wechat-highlight-box.success {
  border-left-color: #10b981; /* emerald-500 */
  background: rgba(16, 185, 129, 0.1);
}

.prose-editor .wechat-highlight-box.error {
  border-left-color: #ef4444; /* red-500 */
  background: rgba(239, 68, 68, 0.1);
}

.prose-editor .wechat-code-block {
  margin: 1.5rem 0;
  border-radius: 0.5rem;
  overflow: hidden;
  background: #0f172a; /* slate-900 */
  border: 1px solid #334155; /* slate-700 */
}

.prose-editor .wechat-section {
  margin: 2rem 0;
  padding: 1.5rem;
  border: 1px solid #334155; /* slate-700 */
  border-radius: 0.5rem;
  background: rgba(30, 41, 59, 0.5); /* slate-800/50 */
}

.prose-editor .wechat-divider {
  margin: 2rem 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #06b6d4, transparent);
  border: none;
}

/* 粘贴提示动画 */
.prose-editor .paste-hint {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

/* 编辑器焦点状态 */
.prose-editor .ProseMirror:focus {
  box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.3);
}

/* 选中文本样式 */
.prose-editor .ProseMirror::selection {
  background: rgba(6, 182, 212, 0.3);
}

.prose-editor .ProseMirror ::-moz-selection {
  background: rgba(6, 182, 212, 0.3);
}

/* 占位符样式 */
.prose-editor .ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #64748b; /* slate-500 */
  pointer-events: none;
  height: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .prose-editor .ProseMirror {
    padding: 0.75rem;
    min-height: 300px;
  }
  
  .prose-editor .ProseMirror h2 {
    font-size: 1.25rem;
  }
}
