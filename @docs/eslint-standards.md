# ESLint 配置与代码质量标准

## 当前ESLint配置总结

### 已放宽的规则（降级为警告）
1. **TypeScript类型规则**
   - `@typescript-eslint/no-explicit-any`: warn - 允许在特定情况下使用any类型
   - `@typescript-eslint/no-unused-vars`: warn - 未使用变量警告而非错误
   - `prefer-const`: warn - 优先使用const的建议

2. **React/Next.js规则**
   - `react/no-unescaped-entities`: off - 完全关闭未转义字符检查
   - `@next/next/no-img-element`: warn - 允许使用img标签（WeChat组件需要）
   - `react/jsx-no-comment-textnodes`: warn - 允许JSX注释
   - `react-hooks/exhaustive-deps`: warn - 放宽Hook依赖检查

### 代码生成/修改时的检查流程

#### 1. 新代码标准
- **必须遵守的错误级别规则**：
  - 语法错误和编译错误必须修复
  - 类型安全问题（除any类型外）必须解决
  - React组件生命周期错误必须修复

#### 2. 可接受的警告类型
- **any类型使用**：在以下情况下可接受
  - TipTap扩展的复杂类型定义
  - DOM操作和第三方库集成
  - 临时性的类型绕过（需要注释说明）

- **img标签使用**：WeChat组件中可以使用
- **Hook依赖**：在确保功能正确的前提下可以忽略

#### 3. 检查命令
```bash
# 运行ESLint检查
pnpm run lint

# 构建时检查（包含类型检查）
pnpm run build
```

### 项目特殊规则说明

#### WeChat组件相关
- ImageContainer组件中使用img标签是被允许的
- WeChat文章组件的样式可能需要特殊的CSS类名

#### TipTap编辑器相关
- 扩展文件中的any类型使用是可接受的
- 命令类型定义可以使用类型断言

#### 管理界面相关
- 管理界面组件应该遵循更严格的类型检查
- 新的管理功能应该避免使用any类型

## 代码质量检查清单

### 新增代码检查项
- [ ] 无语法错误
- [ ] 无类型错误（any类型除外）
- [ ] 组件props有正确的类型定义
- [ ] 事件处理函数有正确的类型
- [ ] API调用有错误处理
- [ ] 响应式设计考虑

### 修改现有代码检查项
- [ ] 不破坏现有功能
- [ ] 保持WeChat组件兼容性
- [ ] 维持cyberpunk主题一致性
- [ ] 不引入新的错误级别问题

## 忽略的现有问题
- 现有文件中的any类型警告（约100+个）
- 图片优化建议（WeChat组件特殊需求）
- 部分Hook依赖警告（功能正确的前提下）
