#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * 简单的图片复制脚本
 * 这个脚本确保构建过程中图片资源正确处理
 */

console.log('📸 开始处理图片资源...');

// 检查 public 目录是否存在
const publicDir = path.join(process.cwd(), 'public');
if (!fs.existsSync(publicDir)) {
  console.log('⚠️  public 目录不存在，跳过图片处理');
  process.exit(0);
}

// 检查图片目录
const imageDirectories = [
  'public/assets',
  'public/posts'
];

let totalImages = 0;

imageDirectories.forEach(dir => {
  const fullPath = path.join(process.cwd(), dir);
  if (fs.existsSync(fullPath)) {
    const files = fs.readdirSync(fullPath, { recursive: true });
    const imageFiles = files.filter(file => {
      const ext = path.extname(file).toLowerCase();
      return ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp'].includes(ext);
    });
    totalImages += imageFiles.length;
    console.log(`📁 ${dir}: 发现 ${imageFiles.length} 个图片文件`);
  }
});

console.log(`✅ 图片处理完成，共处理 ${totalImages} 个图片文件`);
console.log('🚀 准备开始 Next.js 构建...');
