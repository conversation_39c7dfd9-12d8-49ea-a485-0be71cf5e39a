import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      "react/no-unescaped-entities": "off",
      // 暂时放宽类型检查规则，允许在特定情况下使用 any
      "@typescript-eslint/no-explicit-any": "warn",
      "@typescript-eslint/no-unused-vars": "warn",
      "prefer-const": "warn",
      // 允许在 JSX 中使用 img 标签（WeChat 组件需要）
      "@next/next/no-img-element": "warn",
      // 允许 JSX 注释
      "react/jsx-no-comment-textnodes": "warn",
      // 放宽 React Hook 依赖检查
      "react-hooks/exhaustive-deps": "warn",
    },
  },
];

export default eslintConfig;
