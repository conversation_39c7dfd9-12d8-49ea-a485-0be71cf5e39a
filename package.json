{"name": "silicon-based-teahouse", "version": "0.1.0", "private": true, "scripts": {"dev": "TURBOPACK=0 next dev", "build": "npm run copy-images && next build", "start": "next start", "lint": "next lint", "copy-images": "node scripts/copy-images.js"}, "dependencies": {"@next/mdx": "^15.3.3", "@prisma/client": "^6.8.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@tiptap/core": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/html": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "gray-matter": "^4.0.3", "html2canvas": "^1.4.1", "lucide-react": "^0.511.0", "marked": "^15.0.12", "mermaid": "^11.6.0", "next": "15.3.2", "next-themes": "^0.4.6", "prisma": "^6.8.2", "react": "^19.0.0", "react-dom": "^19.0.0", "rehype-stringify": "^10.0.1", "remark": "^15.0.1", "remark-html": "^16.0.1", "tailwind-merge": "^3.3.0", "unified": "^11.0.5", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/better-sqlite3": "^7.6.13", "@types/node": "^20.17.52", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "sass": "^1.89.1", "tailwindcss": "^4", "tw-animate-css": "^1.3.3", "typescript": "^5"}}